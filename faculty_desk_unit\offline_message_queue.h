#ifndef OFFLINE_MESSAGE_QUEUE_H
#define OFFLINE_MESSAGE_QUEUE_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <EEPROM.h>
#include "config.h"

// Message types for queuing
enum MessageType {
  MSG_TYPE_CONSULTATION = 1,
  MSG_TYPE_RESPONSE = 2,
  MSG_TYPE_STATUS_UPDATE = 3,
  MSG_TYPE_HEARTBEAT = 4
};

// Message priority levels
enum MessagePriority {
  PRIORITY_LOW = 1,
  PRIORITY_NORMAL = 2,
  PRIORITY_HIGH = 3,
  PRIORITY_CRITICAL = 4
};

// Message status
enum MessageStatus {
  MSG_STATUS_PENDING = 0,
  MSG_STATUS_SENT = 1,
  MSG_STATUS_FAILED = 2,
  MSG_STATUS_EXPIRED = 3
};

// Queued message structure
struct QueuedMessage {
  uint32_t id;                          // Unique message ID
  MessageType type;                     // Message type
  MessagePriority priority;             // Message priority
  MessageStatus status;                 // Current status
  uint32_t timestamp;                   // Creation timestamp
  uint32_t retry_count;                 // Number of retry attempts
  uint32_t next_retry;                  // Next retry timestamp
  char topic[64];                       // MQTT topic
  char payload[MAX_MESSAGE_LENGTH];     // Message payload
  bool persistent;                      // Should be saved to EEPROM
};

class OfflineMessageQueue {
private:
  static QueuedMessage messageQueue[MAX_QUEUED_MESSAGES];
  static QueuedMessage responseQueue[MAX_QUEUED_RESPONSES];
  static QueuedMessage statusQueue[MAX_QUEUED_STATUS_UPDATES];
  
  static int messageCount;
  static int responseCount;
  static int statusCount;
  static uint32_t nextMessageId;
  static bool initialized;
  static unsigned long lastCleanup;
  static unsigned long lastPersistenceSync;
  
  // EEPROM persistence
  static const int EEPROM_START_ADDR = 0;
  static const int EEPROM_MAGIC = 0xCAFE;
  static const int EEPROM_VERSION = 1;
  
  // Internal methods
  static uint32_t generateMessageId();
  static bool addToQueue(QueuedMessage* queue, int& count, int maxCount, const QueuedMessage& message);
  static bool removeFromQueue(QueuedMessage* queue, int& count, uint32_t messageId);
  static void shiftQueue(QueuedMessage* queue, int& count, int startIndex);
  static void cleanupExpiredMessages();
  static void sortQueueByPriority(QueuedMessage* queue, int count);
  
  // Persistence methods
  static void loadFromEEPROM();
  static void saveToEEPROM();
  static bool isEEPROMValid();
  static void initializeEEPROM();

public:
  // Initialization
  static void init();
  static void update();
  
  // Message queuing
  static bool queueConsultationMessage(const char* topic, const char* payload, MessagePriority priority = PRIORITY_NORMAL);
  static bool queueResponse(const char* topic, const char* payload, MessagePriority priority = PRIORITY_HIGH);
  static bool queueStatusUpdate(const char* topic, const char* payload, MessagePriority priority = PRIORITY_NORMAL);
  static bool queueHeartbeat(const char* topic, const char* payload, MessagePriority priority = PRIORITY_LOW);
  
  // Message processing
  static bool processNextMessage();
  static bool retryFailedMessages();
  static void markMessageSent(uint32_t messageId);
  static void markMessageFailed(uint32_t messageId);
  
  // Queue management
  static int getTotalQueuedMessages();
  static int getPendingMessageCount();
  static int getFailedMessageCount();
  static void clearExpiredMessages();
  static void clearAllQueues();
  
  // Status and diagnostics
  static void printQueueStatus();
  static bool hasHighPriorityMessages();
  static bool hasFailedMessages();
  static unsigned long getOldestMessageAge();
  
  // Persistence
  static void forcePersistenceSync();
  static bool isPersistenceEnabled();
};

// Offline operation manager
class OfflineOperationManager {
private:
  static bool offlineMode;
  static unsigned long offlineStartTime;
  static unsigned long lastSyncAttempt;
  static int syncRetryCount;
  static bool dataChanged;
  
  // Offline state tracking
  static bool lastWifiStatus;
  static bool lastMqttStatus;
  static unsigned long lastSuccessfulSync;
  
public:
  // Initialization
  static void init();
  static void update();
  
  // Offline mode management
  static void enterOfflineMode();
  static void exitOfflineMode();
  static bool isOfflineMode();
  static unsigned long getOfflineDuration();
  
  // Synchronization
  static bool attemptSync();
  static bool syncPendingMessages();
  static bool syncStatusUpdates();
  static void onConnectionRestored();
  
  // Status tracking
  static void updateConnectionStatus(bool wifiConnected, bool mqttConnected);
  static bool shouldAttemptSync();
  static void markDataChanged();
  
  // Recovery operations
  static void performRecoverySync();
  static bool validateSystemState();
  static void resetSyncState();
  
  // Diagnostics
  static void printOfflineStatus();
  static bool hasUnsyncedData();
  static int getUnsyncedMessageCount();
};

// Recovery and synchronization manager
class RecoveryManager {
private:
  static bool recoveryInProgress;
  static unsigned long recoveryStartTime;
  static int recoveryStep;
  static bool stateValidated;
  
public:
  // Recovery operations
  static void init();
  static void startRecovery();
  static void updateRecovery();
  static bool isRecoveryInProgress();
  
  // State synchronization
  static bool synchronizeState();
  static bool validateRemoteState();
  static bool reconcileLocalState();
  
  // Connection recovery
  static bool recoverWifiConnection();
  static bool recoverMqttConnection();
  static bool recoverTimeSync();
  
  // Data integrity
  static bool validateMessageIntegrity();
  static bool repairCorruptedData();
  static void resetToSafeState();
  
  // Status reporting
  static void reportRecoveryStatus();
  static bool isSystemHealthy();
};

// Global instances
extern OfflineMessageQueue messageQueue;
extern OfflineOperationManager offlineManager;
extern RecoveryManager recoveryManager;

// Utility macros
#define QUEUE_MESSAGE(topic, payload, priority) \
  if (!mqttClient.connected()) { \
    messageQueue.queueConsultationMessage(topic, payload, priority); \
  } else { \
    mqttClient.publish(topic, payload, MQTT_QOS); \
  }

#define QUEUE_RESPONSE(topic, payload) \
  if (!mqttClient.connected()) { \
    messageQueue.queueResponse(topic, payload, PRIORITY_HIGH); \
  } else { \
    mqttClient.publish(topic, payload, MQTT_QOS); \
  }

#define QUEUE_STATUS_UPDATE(topic, payload) \
  if (!mqttClient.connected()) { \
    messageQueue.queueStatusUpdate(topic, payload, PRIORITY_NORMAL); \
  } else { \
    mqttClient.publish(topic, payload, MQTT_QOS); \
  }

#endif // OFFLINE_MESSAGE_QUEUE_H
