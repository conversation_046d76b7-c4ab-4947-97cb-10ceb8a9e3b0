#include "offline_message_queue.h"

// Static member definitions for OfflineMessageQueue
QueuedMessage OfflineMessageQueue::messageQueue[MAX_QUEUED_MESSAGES];
QueuedMessage OfflineMessageQueue::responseQueue[MAX_QUEUED_RESPONSES];
QueuedMessage OfflineMessageQueue::statusQueue[MAX_QUEUED_STATUS_UPDATES];

int OfflineMessageQueue::messageCount = 0;
int OfflineMessageQueue::responseCount = 0;
int OfflineMessageQueue::statusCount = 0;
uint32_t OfflineMessageQueue::nextMessageId = 1;
bool OfflineMessageQueue::initialized = false;
unsigned long OfflineMessageQueue::lastCleanup = 0;
unsigned long OfflineMessageQueue::lastPersistenceSync = 0;

// Static member definitions for OfflineOperationManager
bool OfflineOperationManager::offlineMode = false;
unsigned long OfflineOperationManager::offlineStartTime = 0;
unsigned long OfflineOperationManager::lastSyncAttempt = 0;
int OfflineOperationManager::syncRetryCount = 0;
bool OfflineOperationManager::dataChanged = false;
bool OfflineOperationManager::lastWifiStatus = false;
bool OfflineOperationManager::lastMqttStatus = false;
unsigned long OfflineOperationManager::lastSuccessfulSync = 0;

// Static member definitions for RecoveryManager
bool RecoveryManager::recoveryInProgress = false;
unsigned long RecoveryManager::recoveryStartTime = 0;
int RecoveryManager::recoveryStep = 0;
bool RecoveryManager::stateValidated = false;

// ================================
// OfflineMessageQueue Implementation
// ================================

void OfflineMessageQueue::init() {
  if (initialized) return;
  
  DEBUG_PRINTLN("🔄 Initializing Offline Message Queue System");
  
  // Initialize EEPROM if persistence is enabled
  if (MESSAGE_PERSISTENCE_ENABLED) {
    EEPROM.begin(OFFLINE_STORAGE_SIZE);
    if (isEEPROMValid()) {
      loadFromEEPROM();
      DEBUG_PRINTLN("📂 Loaded queued messages from EEPROM");
    } else {
      initializeEEPROM();
      DEBUG_PRINTLN("🆕 Initialized new EEPROM storage");
    }
  }
  
  // Clear any expired messages
  cleanupExpiredMessages();
  
  initialized = true;
  DEBUG_PRINTF("✅ Message Queue initialized - Messages: %d, Responses: %d, Status: %d\n", 
               messageCount, responseCount, statusCount);
}

void OfflineMessageQueue::update() {
  if (!initialized) return;
  
  unsigned long now = millis();
  
  // Periodic cleanup
  if (now - lastCleanup > QUEUE_CLEANUP_INTERVAL) {
    cleanupExpiredMessages();
    lastCleanup = now;
  }
  
  // Periodic persistence sync
  if (MESSAGE_PERSISTENCE_ENABLED && now - lastPersistenceSync > 30000) {
    saveToEEPROM();
    lastPersistenceSync = now;
  }
  
  // Process retry attempts
  retryFailedMessages();
}

uint32_t OfflineMessageQueue::generateMessageId() {
  return nextMessageId++;
}

bool OfflineMessageQueue::queueConsultationMessage(const char* topic, const char* payload, MessagePriority priority) {
  QueuedMessage msg;
  msg.id = generateMessageId();
  msg.type = MSG_TYPE_CONSULTATION;
  msg.priority = priority;
  msg.status = MSG_STATUS_PENDING;
  msg.timestamp = millis();
  msg.retry_count = 0;
  msg.next_retry = 0;
  msg.persistent = true;
  
  strncpy(msg.topic, topic, sizeof(msg.topic) - 1);
  strncpy(msg.payload, payload, sizeof(msg.payload) - 1);
  msg.topic[sizeof(msg.topic) - 1] = '\0';
  msg.payload[sizeof(msg.payload) - 1] = '\0';
  
  bool success = addToQueue(messageQueue, messageCount, MAX_QUEUED_MESSAGES, msg);
  if (success) {
    DEBUG_PRINTF("📥 Queued consultation message ID %d (Priority: %d)\n", msg.id, priority);
    OfflineOperationManager::markDataChanged();
  }
  return success;
}

bool OfflineMessageQueue::queueResponse(const char* topic, const char* payload, MessagePriority priority) {
  QueuedMessage msg;
  msg.id = generateMessageId();
  msg.type = MSG_TYPE_RESPONSE;
  msg.priority = priority;
  msg.status = MSG_STATUS_PENDING;
  msg.timestamp = millis();
  msg.retry_count = 0;
  msg.next_retry = 0;
  msg.persistent = true;
  
  strncpy(msg.topic, topic, sizeof(msg.topic) - 1);
  strncpy(msg.payload, payload, sizeof(msg.payload) - 1);
  msg.topic[sizeof(msg.topic) - 1] = '\0';
  msg.payload[sizeof(msg.payload) - 1] = '\0';
  
  bool success = addToQueue(responseQueue, responseCount, MAX_QUEUED_RESPONSES, msg);
  if (success) {
    DEBUG_PRINTF("📤 Queued response message ID %d (Priority: %d)\n", msg.id, priority);
    OfflineOperationManager::markDataChanged();
  }
  return success;
}

bool OfflineMessageQueue::queueStatusUpdate(const char* topic, const char* payload, MessagePriority priority) {
  QueuedMessage msg;
  msg.id = generateMessageId();
  msg.type = MSG_TYPE_STATUS_UPDATE;
  msg.priority = priority;
  msg.status = MSG_STATUS_PENDING;
  msg.timestamp = millis();
  msg.retry_count = 0;
  msg.next_retry = 0;
  msg.persistent = false; // Status updates are not persistent by default
  
  strncpy(msg.topic, topic, sizeof(msg.topic) - 1);
  strncpy(msg.payload, payload, sizeof(msg.payload) - 1);
  msg.topic[sizeof(msg.topic) - 1] = '\0';
  msg.payload[sizeof(msg.payload) - 1] = '\0';
  
  bool success = addToQueue(statusQueue, statusCount, MAX_QUEUED_STATUS_UPDATES, msg);
  if (success) {
    DEBUG_PRINTF("📊 Queued status update ID %d\n", msg.id);
    OfflineOperationManager::markDataChanged();
  }
  return success;
}

bool OfflineMessageQueue::addToQueue(QueuedMessage* queue, int& count, int maxCount, const QueuedMessage& message) {
  if (count >= maxCount) {
    // Queue is full, try to remove oldest low-priority message
    for (int i = 0; i < count; i++) {
      if (queue[i].priority == PRIORITY_LOW && queue[i].status != MSG_STATUS_PENDING) {
        shiftQueue(queue, count, i);
        count--;
        break;
      }
    }
    
    // If still full, reject the message
    if (count >= maxCount) {
      DEBUG_PRINTLN("⚠️ Message queue full, dropping message");
      return false;
    }
  }
  
  // Add message to queue
  queue[count] = message;
  count++;
  
  // Sort by priority
  sortQueueByPriority(queue, count);
  
  return true;
}

void OfflineMessageQueue::sortQueueByPriority(QueuedMessage* queue, int count) {
  // Simple bubble sort by priority (high to low)
  for (int i = 0; i < count - 1; i++) {
    for (int j = 0; j < count - i - 1; j++) {
      if (queue[j].priority < queue[j + 1].priority) {
        QueuedMessage temp = queue[j];
        queue[j] = queue[j + 1];
        queue[j + 1] = temp;
      }
    }
  }
}

bool OfflineMessageQueue::processNextMessage() {
  // Process highest priority pending message from any queue
  QueuedMessage* nextMsg = nullptr;
  QueuedMessage* sourceQueue = nullptr;
  int* sourceCount = nullptr;
  int msgIndex = -1;
  
  // Find highest priority pending message
  MessagePriority highestPriority = PRIORITY_LOW;
  
  // Check response queue first (highest priority)
  for (int i = 0; i < responseCount; i++) {
    if (responseQueue[i].status == MSG_STATUS_PENDING && responseQueue[i].priority >= highestPriority) {
      nextMsg = &responseQueue[i];
      sourceQueue = responseQueue;
      sourceCount = &responseCount;
      msgIndex = i;
      highestPriority = responseQueue[i].priority;
    }
  }
  
  // Check message queue
  for (int i = 0; i < messageCount; i++) {
    if (messageQueue[i].status == MSG_STATUS_PENDING && messageQueue[i].priority > highestPriority) {
      nextMsg = &messageQueue[i];
      sourceQueue = messageQueue;
      sourceCount = &messageCount;
      msgIndex = i;
      highestPriority = messageQueue[i].priority;
    }
  }
  
  // Check status queue
  for (int i = 0; i < statusCount; i++) {
    if (statusQueue[i].status == MSG_STATUS_PENDING && statusQueue[i].priority > highestPriority) {
      nextMsg = &statusQueue[i];
      sourceQueue = statusQueue;
      sourceCount = &statusCount;
      msgIndex = i;
      highestPriority = statusQueue[i].priority;
    }
  }
  
  if (nextMsg == nullptr) {
    return false; // No pending messages
  }
  
  // Attempt to send the message
  extern PubSubClient mqttClient;
  if (mqttClient.connected()) {
    bool success = mqttClient.publish(nextMsg->topic, nextMsg->payload, MQTT_QOS);
    if (success) {
      markMessageSent(nextMsg->id);
      DEBUG_PRINTF("📤 Sent queued message ID %d\n", nextMsg->id);
      return true;
    } else {
      markMessageFailed(nextMsg->id);
      DEBUG_PRINTF("❌ Failed to send queued message ID %d\n", nextMsg->id);
      return false;
    }
  }
  
  return false;
}

int OfflineMessageQueue::getTotalQueuedMessages() {
  return messageCount + responseCount + statusCount;
}

int OfflineMessageQueue::getPendingMessageCount() {
  int pending = 0;
  
  for (int i = 0; i < messageCount; i++) {
    if (messageQueue[i].status == MSG_STATUS_PENDING) pending++;
  }
  for (int i = 0; i < responseCount; i++) {
    if (responseQueue[i].status == MSG_STATUS_PENDING) pending++;
  }
  for (int i = 0; i < statusCount; i++) {
    if (statusQueue[i].status == MSG_STATUS_PENDING) pending++;
  }
  
  return pending;
}

void OfflineMessageQueue::printQueueStatus() {
  DEBUG_PRINTLN("📊 Message Queue Status:");
  DEBUG_PRINTF("   Total Messages: %d (Pending: %d)\n", getTotalQueuedMessages(), getPendingMessageCount());
  DEBUG_PRINTF("   Consultation: %d, Responses: %d, Status: %d\n", messageCount, responseCount, statusCount);
  DEBUG_PRINTF("   High Priority: %s\n", hasHighPriorityMessages() ? "YES" : "NO");
  DEBUG_PRINTF("   Failed Messages: %s\n", hasFailedMessages() ? "YES" : "NO");
}

// ================================
// OfflineOperationManager Implementation
// ================================

void OfflineOperationManager::init() {
  DEBUG_PRINTLN("🔄 Initializing Offline Operation Manager");
  offlineMode = false;
  offlineStartTime = 0;
  lastSyncAttempt = 0;
  syncRetryCount = 0;
  dataChanged = false;
  lastSuccessfulSync = millis();
  DEBUG_PRINTLN("✅ Offline Operation Manager initialized");
}

void OfflineOperationManager::enterOfflineMode() {
  if (!offlineMode) {
    offlineMode = true;
    offlineStartTime = millis();
    syncRetryCount = 0;
    DEBUG_PRINTLN("🔌 Entered offline mode");
  }
}

void OfflineOperationManager::exitOfflineMode() {
  if (offlineMode) {
    offlineMode = false;
    unsigned long offlineDuration = millis() - offlineStartTime;
    DEBUG_PRINTF("🌐 Exited offline mode (Duration: %lu ms)\n", offlineDuration);
    
    // Trigger recovery sync
    onConnectionRestored();
  }
}

void OfflineOperationManager::onConnectionRestored() {
  DEBUG_PRINTLN("🔄 Connection restored - starting recovery sync");
  RecoveryManager::startRecovery();
  lastSuccessfulSync = millis();
  syncRetryCount = 0;
}

bool OfflineOperationManager::hasUnsyncedData() {
  return OfflineMessageQueue::getPendingMessageCount() > 0;
}

// ================================
// RecoveryManager Implementation
// ================================

void RecoveryManager::init() {
  DEBUG_PRINTLN("🔄 Initializing Recovery Manager");
  recoveryInProgress = false;
  recoveryStartTime = 0;
  recoveryStep = 0;
  stateValidated = false;
  DEBUG_PRINTLN("✅ Recovery Manager initialized");
}

void RecoveryManager::startRecovery() {
  if (recoveryInProgress) return;
  
  recoveryInProgress = true;
  recoveryStartTime = millis();
  recoveryStep = 0;
  stateValidated = false;
  
  DEBUG_PRINTLN("🚀 Starting system recovery process");
}

bool RecoveryManager::synchronizeState() {
  DEBUG_PRINTLN("🔄 Synchronizing system state");
  
  // Process all queued messages
  int processed = 0;
  while (OfflineMessageQueue::processNextMessage() && processed < 10) {
    processed++;
    delay(100); // Small delay between messages
  }
  
  if (processed > 0) {
    DEBUG_PRINTF("📤 Synchronized %d queued messages\n", processed);
  }
  
  return true;
}

// Global instances
OfflineMessageQueue messageQueue;
OfflineOperationManager offlineManager;
RecoveryManager recoveryManager;
